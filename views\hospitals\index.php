<?php
/**
 * Hospitals List View
 *
 * This file displays the list of hospitals.
 */

// Set page title
$pageTitle = 'Hospitals';
$pageSubtitle = 'Manage hospital facilities and their information';

// Start output buffering
ob_start();
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Hospitals</li>
    </ol>
</nav>

<!-- Header Section -->
<div class="card card-glass border-0 mb-4 fade-in-up">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div>
                    <h1 class="h2 text-gradient mb-1">Hospitals Management</h1>
                    <p class="text-muted mb-0">Manage hospital facilities and their information</p>
                    <small class="text-info">
                        <?php echo count($hospitals); ?> hospitals registered
                    </small>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <?php if (hasPermission('manage_hospitals')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/hospitals/create" class="btn btn-primary btn-lg">
                        Add Hospital
                    </a>
                    <?php endif; ?>

                    <div class="dropdown">
                        <button class="btn btn-outline-primary btn-lg dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><h6 class="dropdown-header">Export Formats</h6></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/hospitals/export?format=pdf">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>PDF Report
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/hospitals/export?format=excel">
                                <i class="fas fa-file-excel me-2 text-success"></i>Excel Spreadsheet
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/hospitals/export?format=csv">
                                <i class="fas fa-file-csv me-2 text-info"></i>CSV Data
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="printHospitals()">
                                <i class="fas fa-print me-2 text-secondary"></i>Print List
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card primary fade-in-up">
            <div class="card-body">
                <div class="stats-number"><?php echo count($hospitals); ?></div>
                <div class="stats-text">Total Hospitals</div>
                <i class="fas fa-hospital stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card success fade-in-up" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="stats-number"><?php echo array_sum(array_column($hospitals, 'department_count')); ?></div>
                <div class="stats-text">Total Departments</div>
                <i class="fas fa-building stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card info fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="stats-number"><?php echo array_sum(array_column($hospitals, 'device_count')); ?></div>
                <div class="stats-text">Total Devices</div>
                <i class="fas fa-microscope stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="stats-number"><?php echo count(array_unique(array_column($hospitals, 'country'))); ?></div>
                <div class="stats-text">Countries</div>
                <i class="fas fa-globe stats-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Hospitals Table -->
<div class="card fade-in-up" style="animation-delay: 0.4s;">
    <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>Hospitals List
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('table')" id="tableViewBtn">
                    <i class="fas fa-table"></i> Table
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('cards')" id="cardsViewBtn">
                    <i class="fas fa-th-large"></i> Cards
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="hospitals-table">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <i class="fas fa-hospital me-1"></i>Hospital
                            </th>
                            <th>
                                <i class="fas fa-map-marker-alt me-1"></i>Location
                            </th>
                            <th>
                                <i class="fas fa-phone me-1"></i>Contact
                            </th>
                            <th>
                                <i class="fas fa-building me-1"></i>Departments
                            </th>
                            <th>
                                <i class="fas fa-microscope me-1"></i>Devices
                            </th>
                            <th>
                                <i class="fas fa-cogs me-1"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($hospitals)): ?>
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-hospital fa-3x mb-3 opacity-50"></i>
                                        <h5>No Hospitals</h5>
                                        <p>No hospitals found in the system</p>
                                        <?php if (hasPermission('manage_hospitals')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/hospitals/create" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Add First Hospital
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($hospitals as $hospital): ?>
                                <tr class="hospital-row" data-hospital-id="<?php echo $hospital['id']; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="hospital-icon me-3">
                                                <i class="fas fa-hospital text-primary fa-lg"></i>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($hospital['name']); ?></strong>
                                                <?php if (!empty($hospital['address'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($hospital['address'], 0, 50)); ?>...</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-city text-info me-1"></i>
                                            <?php echo htmlspecialchars($hospital['city']); ?>
                                            <br>
                                            <i class="fas fa-flag text-success me-1"></i>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($hospital['country']); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-phone text-primary me-1"></i>
                                            <a href="tel:<?php echo htmlspecialchars($hospital['phone']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($hospital['phone']); ?>
                                            </a>
                                            <?php if (!empty($hospital['email'])): ?>
                                                <br>
                                                <i class="fas fa-envelope text-info me-1"></i>
                                                <a href="mailto:<?php echo htmlspecialchars($hospital['email']); ?>" class="text-decoration-none small">
                                                    <?php echo htmlspecialchars($hospital['email']); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info fs-6 px-3 py-2">
                                            <i class="fas fa-building me-1"></i>
                                            <?php echo (int)$hospital['department_count']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success fs-6 px-3 py-2">
                                            <i class="fas fa-microscope me-1"></i>
                                            <?php echo (int)$hospital['device_count']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $hospital['id']; ?>"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="View Hospital">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <?php if (hasPermission('manage_hospitals')): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   data-bs-toggle="tooltip"
                                                   title="Edit Hospital">
                                                    <i class="fas fa-edit"></i>
                                                </a>

                                                <button type="button"
                                                        class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal"
                                                        data-id="<?php echo $hospital['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($hospital['name']); ?>"
                                                        title="Delete Hospital">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cards View -->
        <div class="row g-3 p-3" id="cardsView" style="display: none;">
            <?php if (!empty($hospitals)): ?>
                <?php foreach ($hospitals as $hospital): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card hospital-card h-100 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-hospital me-2"></i>
                                        <?php echo htmlspecialchars($hospital['name']); ?>
                                    </h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">Location</small>
                                    <div>
                                        <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                        <?php echo htmlspecialchars($hospital['city']); ?>, <?php echo htmlspecialchars($hospital['country']); ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Contact</small>
                                    <div>
                                        <i class="fas fa-phone text-primary me-1"></i>
                                        <?php echo htmlspecialchars($hospital['phone']); ?>
                                    </div>
                                </div>

                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-info"><?php echo (int)$hospital['department_count']; ?></h4>
                                            <small class="text-muted">Departments</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo (int)$hospital['device_count']; ?></h4>
                                        <small class="text-muted">Devices</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    <?php if (hasPermission('manage_hospitals')): ?>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Export Options</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <form action="<?php echo getBaseUrl(); ?>/hospitals/export" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="mb-3">
                        <label for="export_type" class="form-label">Export Format</label>
                        <select class="form-select" id="export_type" name="export_type">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete hospital <span id="hospitalName"></span>?</p>
                <p class="text-danger">This action cannot be undone and will remove all associated data</p>
            </div>
            <div class="modal-footer">
                <form action="<?php echo getBaseUrl(); ?>/hospitals/delete" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" id="deleteId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add enhanced scripts
$scripts = '
<style>
.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hospital-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hospital-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hospital-row {
    transition: background-color 0.2s ease;
}

.hospital-row:hover {
    background-color: rgba(0,123,255,0.05);
}

.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@media print {
    .btn, .dropdown, .breadcrumb {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }

    #cardsView {
        display: none !important;
    }

    #tableView {
        display: block !important;
    }
}
</style>

<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $("#hospitals-table").DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, "asc"]],
        language: {
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        },
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on actions column
        ]
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"tooltip\"]"));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Set up delete modal
    $("#deleteModal").on("show.bs.modal", function(event) {
        var button = $(event.relatedTarget);
        var id = button.data("id");
        var name = button.data("name");

        $("#deleteId").val(id);
        $("#hospitalName").text(name);
    });

    // Enhanced hospital row interactions
    $(".hospital-row").on("mouseenter", function() {
        $(this).find(".btn-group").addClass("show-actions");
    }).on("mouseleave", function() {
        $(this).find(".btn-group").removeClass("show-actions");
    });

    // Restore saved view preference
    const savedView = localStorage.getItem("hospitalsView");
    if (savedView === "cards") {
        toggleView("cards");
    }
});

// View toggle functionality
function toggleView(viewType) {
    const tableView = document.getElementById("tableView");
    const cardsView = document.getElementById("cardsView");
    const tableBtn = document.getElementById("tableViewBtn");
    const cardsBtn = document.getElementById("cardsViewBtn");

    if (viewType === "table") {
        tableView.style.display = "block";
        cardsView.style.display = "none";
        tableBtn.classList.add("active");
        cardsBtn.classList.remove("active");
        localStorage.setItem("hospitalsView", "table");
    } else {
        tableView.style.display = "none";
        cardsView.style.display = "block";
        cardsBtn.classList.add("active");
        tableBtn.classList.remove("active");
        localStorage.setItem("hospitalsView", "cards");
    }
}

// Print hospitals list
function printHospitals() {
    // Ensure table view is shown for printing
    const currentView = localStorage.getItem("hospitalsView");
    toggleView("table");

    window.print();

    // Restore previous view after printing
    if (currentView === "cards") {
        setTimeout(() => toggleView("cards"), 1000);
    }
}

// Enhanced search functionality
function searchHospitals() {
    const searchTerm = document.getElementById("hospitalSearch").value.toLowerCase();
    const rows = document.querySelectorAll(".hospital-row");

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = "";
        } else {
            row.style.display = "none";
        }
    });
}

// Export functionality
function exportHospitals(format) {
    const form = document.createElement("form");
    form.method = "POST";
    form.action = "' . getBaseUrl() . '/hospitals/export";

    const formatInput = document.createElement("input");
    formatInput.type = "hidden";
    formatInput.name = "format";
    formatInput.value = format;

    const csrfInput = document.createElement("input");
    csrfInput.type = "hidden";
    csrfInput.name = "csrf_token";
    csrfInput.value = "' . generateCSRFToken() . '";

    form.appendChild(formatInput);
    form.appendChild(csrfInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    showToast("Export started", "info");
}

// Show toast notification
function showToast(message, type = "info") {
    const toastContainer = document.getElementById("toast-container") || createToastContainer();

    const toast = document.createElement("div");
    toast.className = `toast align-items-center text-white bg-${type === "error" ? "danger" : type} border-0`;
    toast.setAttribute("role", "alert");
    toast.setAttribute("aria-live", "assertive");
    toast.setAttribute("aria-atomic", "true");

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === "success" ? "check" : type === "error" ? "times" : "info"}-circle me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it hides
    toast.addEventListener("hidden.bs.toast", function() {
        toast.remove();
    });
}

// Create toast container if it doesn\'t exist
function createToastContainer() {
    const container = document.createElement("div");
    container.id = "toast-container";
    container.className = "toast-container position-fixed top-0 end-0 p-3";
    container.style.zIndex = "9999";
    document.body.appendChild(container);
    return container;
}

// Keyboard shortcuts
document.addEventListener("keydown", function(e) {
    // Ctrl/Cmd + N for new hospital
    if ((e.ctrlKey || e.metaKey) && e.key === "n") {
        e.preventDefault();
        const addBtn = document.querySelector("a[href*=\"/hospitals/create\"]");
        if (addBtn) addBtn.click();
    }

    // Ctrl/Cmd + P for print
    if ((e.ctrlKey || e.metaKey) && e.key === "p") {
        e.preventDefault();
        printHospitals();
    }

    // V key to toggle view
    if (e.key === "v" && !e.ctrlKey && !e.metaKey) {
        const currentView = localStorage.getItem("hospitalsView") || "table";
        toggleView(currentView === "table" ? "cards" : "table");
    }
});
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
